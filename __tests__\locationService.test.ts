import LocationService from '../services/locationService';

// Mock expo-location
jest.mock('expo-location', () => ({
  requestForegroundPermissionsAsync: jest.fn(),
  getCurrentPositionAsync: jest.fn(),
  reverseGeocodeAsync: jest.fn(),
  Accuracy: {
    Balanced: 'balanced',
    Low: 'low'
  }
}));

describe('LocationService', () => {
  beforeEach(() => {
    // Reset the singleton instance
    LocationService.reset();
    jest.clearAllMocks();
  });

  it('should be a singleton', () => {
    const instance1 = LocationService;
    const instance2 = LocationService;
    expect(instance1).toBe(instance2);
  });

  it('should handle permission denied gracefully', async () => {
    const mockLocation = require('expo-location');
    mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'denied' });

    const result = await LocationService.requestLocation();
    
    expect(result.error).toBe(true);
    expect(result.address).toBe('Location access denied');
  });

  it('should handle successful location request', async () => {
    const mockLocation = require('expo-location');
    const mockPosition = {
      coords: {
        latitude: 6.5244,
        longitude: 3.3792,
        altitude: null,
        accuracy: 10,
        altitudeAccuracy: null,
        heading: null,
        speed: null
      },
      timestamp: Date.now()
    };

    mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'granted' });
    mockLocation.getCurrentPositionAsync.mockResolvedValue(mockPosition);
    mockLocation.reverseGeocodeAsync.mockResolvedValue([{
      region: 'Lagos',
      city: 'Lagos',
      country: 'Nigeria'
    }]);

    const result = await LocationService.requestLocation();
    
    expect(result.error).toBe(false);
    expect(result.location).toEqual(mockPosition);
    expect(result.region).toBe('Lagos');
  });

  it('should cache location results', async () => {
    const mockLocation = require('expo-location');
    const mockPosition = {
      coords: { latitude: 6.5244, longitude: 3.3792, altitude: null, accuracy: 10, altitudeAccuracy: null, heading: null, speed: null },
      timestamp: Date.now()
    };

    mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'granted' });
    mockLocation.getCurrentPositionAsync.mockResolvedValue(mockPosition);
    mockLocation.reverseGeocodeAsync.mockResolvedValue([{ region: 'Lagos', city: 'Lagos' }]);

    // First request
    await LocationService.requestLocation();
    
    // Second request should use cache
    const result = await LocationService.requestLocation();
    
    expect(mockLocation.getCurrentPositionAsync).toHaveBeenCalledTimes(1);
    expect(result.location).toEqual(mockPosition);
  });

  it('should notify subscribers of location updates', async () => {
    const mockCallback = jest.fn();
    const unsubscribe = LocationService.subscribe(mockCallback);

    const mockLocation = require('expo-location');
    mockLocation.requestForegroundPermissionsAsync.mockResolvedValue({ status: 'granted' });
    mockLocation.getCurrentPositionAsync.mockResolvedValue({
      coords: { latitude: 6.5244, longitude: 3.3792, altitude: null, accuracy: 10, altitudeAccuracy: null, heading: null, speed: null },
      timestamp: Date.now()
    });

    await LocationService.requestLocation();

    expect(mockCallback).toHaveBeenCalled();
    unsubscribe();
  });
});
