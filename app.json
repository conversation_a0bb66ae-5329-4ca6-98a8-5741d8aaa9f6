{"expo": {"name": "ServeEz", "slug": "<PERSON><PERSON>", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "<PERSON><PERSON>", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "bundleIdentifier": "com.webinnovation.serveez", "associatedDomains": ["applinks:njkllbogrrqwxxgmsmyr.supabase.co"], "icon": {"dark": "./assets/images/icons/Dark.png", "light": "./assets/images/icons/Light.png", "tinted": "./assets/images/icons/Tinted.png"}, "infoPlist": {"ITSAppUsesNonExemptEncryption": false, "googleMapsApiKey": "AIzaSyAlmHKvAqkFuPSXX2RU0VZ3zXgHajZoyXU"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "monochromeImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "./google-services.json", "permissions": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_LOCATION"], "package": "com.webinnovation.serveez", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "<PERSON><PERSON>"}], "category": ["BROWSABLE", "DEFAULT"]}], "googleMaps": {"apiKey": "AIzaSyAlmHKvAqkFuPSXX2RU0VZ3zXgHajZoyXU"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff", "dark": {"image": "./assets/images/splash-icon-light.png", "backgroundColor": "#000000"}}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow ServeEz to use your location.", "locationWhenInUsePermission": "ServeEz needs your location to find nearby services.", "isIosBackgroundLocationEnabled": true, "isAndroidBackgroundLocationEnabled": true}], ["expo-notifications", {"icon": "./assets/images/adaptive-icon.png", "color": "#ffffff", "sounds": ["./assets/sounds/notification.wav"], "defaultChannel": "default", "enableBackgroundRemoteNotifications": true}], ["@sentry/react-native/expo", {"url": "https://sentry.io/", "project": "<PERSON><PERSON>", "organization": "<PERSON><PERSON>"}], "expo-web-browser", "expo-build-properties"], "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": false}, "eas": {"projectId": "3049fab5-d252-4fc0-8109-55b2dd42ced3"}}}}