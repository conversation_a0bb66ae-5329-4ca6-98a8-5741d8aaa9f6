import React, { useState, useMemo } from 'react';
import { View, Text, TouchableOpacity, Dimensions } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Colors } from '../constants/Colors';
import { ScaledSheet } from 'react-native-size-matters';

const { width: screenWidth } = Dimensions.get('window');

interface CalendarProps {
  selectedDate?: string;
  onDateSelect: (date: string) => void;
  isDark?: boolean;
  colors?: any;
  isSmallScreen?: boolean;
}

interface CalendarDay {
  date: number;
  fullDate: string;
  isCurrentMonth: boolean;
  isToday: boolean;
  isPast: boolean;
}

const Calendar: React.FC<CalendarProps> = ({
  selectedDate,
  onDateSelect,
  isDark = false,
  colors,
  isSmallScreen = false
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date());

  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];

  const calendarData = useMemo(() => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Generate today's date string in the same format for comparison
    const todayYear = today.getFullYear();
    const todayMonth = String(today.getMonth() + 1).padStart(2, '0');
    const todayDay = String(today.getDate()).padStart(2, '0');
    const todayString = `${todayYear}-${todayMonth}-${todayDay}`;

    // First day of the month and last day
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    
    // Start from Sunday of the week containing the first day
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - startDate.getDay());
    
    // Generate 42 days (6 weeks) to fill the calendar grid
    const days: CalendarDay[] = [];
    const currentDate = new Date(startDate);
    
    for (let i = 0; i < 42; i++) {
      const dateObj = new Date(currentDate);
      const isCurrentMonth = dateObj.getMonth() === month;
      const isPast = dateObj < today;

      // Format date as YYYY-MM-DD without timezone conversion
      const year = dateObj.getFullYear();
      const month_num = String(dateObj.getMonth() + 1).padStart(2, '0');
      const day_num = String(dateObj.getDate()).padStart(2, '0');
      const fullDate = `${year}-${month_num}-${day_num}`;

      // Compare date strings instead of time for accurate today detection
      const isToday = fullDate === todayString;

      days.push({
        date: dateObj.getDate(),
        fullDate: fullDate,
        isCurrentMonth,
        isToday,
        isPast
      });

      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    return days;
  }, [currentMonth]);

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  const canNavigatePrev = () => {
    const prevMonth = new Date(currentMonth);
    prevMonth.setMonth(prevMonth.getMonth() - 1);
    const today = new Date();
    return prevMonth.getFullYear() > today.getFullYear() || 
           (prevMonth.getFullYear() === today.getFullYear() && prevMonth.getMonth() >= today.getMonth());
  };

  const renderDay = (day: CalendarDay) => {
    const isSelected = selectedDate === day.fullDate;
    // Only disable past dates, allow future dates from any month
    const isDisabled = day.isPast;
    
    return (
      <TouchableOpacity
        key={day.fullDate}
        style={[
          styles.dayButton,
          isSelected && [styles.selectedDay, { backgroundColor: Colors.primary }],
          day.isToday && !isSelected && [styles.todayButton, { borderColor: Colors.primary }],
          isDark && styles.darkDayButton,
          isSmallScreen && styles.smallScreenDayButton,
          isDisabled && styles.disabledDayButton
        ]}
        onPress={() => !isDisabled && onDateSelect(day.fullDate)}
        disabled={isDisabled}
      >
        <Text style={[
          styles.dayText,
          isSelected && styles.selectedDayText,
          day.isToday && !isSelected && [styles.todayText, { color: Colors.primary }],
          isDisabled && styles.disabledDayText,
          !day.isCurrentMonth && !isSelected && styles.otherMonthDayText,
          isDark && !isSelected && !isDisabled && { color: colors?.text || '#fff' },
          isSmallScreen && styles.smallScreenDayText
        ]}>
          {day.date}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[
      styles.container, 
      isDark && { backgroundColor: colors?.cardBackground || '#1E293B' },
      isSmallScreen && styles.smallScreenContainer
    ]}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={[
            styles.navButton, 
            !canNavigatePrev() && styles.disabledNavButton,
            isDark && { backgroundColor: colors?.border || '#334155' }
          ]}
          onPress={() => navigateMonth('prev')}
          disabled={!canNavigatePrev()}
        >
          <Ionicons 
            name="chevron-back" 
            size={isSmallScreen ? 18 : 22} 
            color={!canNavigatePrev() ? '#94A3B8' : (isDark ? colors?.text || '#fff' : '#475569')} 
          />
        </TouchableOpacity>
        
        <Text style={[
          styles.monthYear,
          isDark && { color: colors?.text || '#fff' },
          isSmallScreen && styles.smallScreenMonthYear
        ]}>
          {monthNames[currentMonth.getMonth()]} {currentMonth.getFullYear()}
        </Text>
        
        <TouchableOpacity
          style={[
            styles.navButton,
            isDark && { backgroundColor: colors?.border || '#334155' }
          ]}
          onPress={() => navigateMonth('next')}
        >
          <Ionicons 
            name="chevron-forward" 
            size={isSmallScreen ? 18 : 22} 
            color={isDark ? colors?.text || '#fff' : '#475569'} 
          />
        </TouchableOpacity>
      </View>

      {/* Day names */}
      <View style={styles.dayNamesRow}>
        {dayNames.map((dayName) => (
          <Text key={dayName} style={[
            styles.dayName,
            isDark && { color: colors?.subtext || '#94A3B8' },
            isSmallScreen && styles.smallScreenDayName
          ]}>
            {dayName}
          </Text>
        ))}
      </View>

      {/* Calendar grid */}
      <View style={styles.calendarGrid}>
        {Array.from({ length: 6 }, (_, weekIndex) => (
          <View key={weekIndex} style={styles.weekRow}>
            {calendarData.slice(weekIndex * 7, (weekIndex + 1) * 7).map(renderDay)}
          </View>
        ))}
      </View>
    </View>
  );
};

const styles = ScaledSheet.create({
  container: {
    backgroundColor: '#FFFFFF',
    borderRadius: '16@ms',
    padding: '16@ms',
    marginBottom: '24@ms',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },
  smallScreenContainer: {
    padding: '12@ms',
    borderRadius: '12@ms',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: '16@ms',
  },
  navButton: {
    padding: '8@ms',
    borderRadius: '8@ms',
    backgroundColor: '#F8FAFC',
  },
  disabledNavButton: {
    opacity: 0.4,
  },
  monthYear: {
    fontSize: '18@ms',
    fontFamily: 'Urbanist-Bold',
    color: '#1E293B',
  },
  smallScreenMonthYear: {
    fontSize: '16@ms',
  },
  dayNamesRow: {
    flexDirection: 'row',
    marginBottom: '8@ms',
  },
  dayName: {
    flex: 1,
    textAlign: 'center',
    fontSize: '12@ms',
    fontFamily: 'Urbanist-SemiBold',
    color: '#64748B',
    paddingVertical: '8@ms',
  },
  smallScreenDayName: {
    fontSize: '10@ms',
    paddingVertical: '6@ms',
  },
  calendarGrid: {
    gap: '4@ms',
  },
  weekRow: {
    flexDirection: 'row',
    gap: '4@ms',
  },
  dayButton: {
    flex: 1,
    aspectRatio: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: '8@ms',
    backgroundColor: 'transparent',
  },
  selectedDay: {
    backgroundColor: Colors.primary,
  },
  todayButton: {
    backgroundColor: 'rgba(79, 70, 229, 0.1)',
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  darkDayButton: {
    backgroundColor: 'transparent',
  },
  disabledDayButton: {
    opacity: 0.3,
  },
  smallScreenDayButton: {
    borderRadius: '6@ms',
  },
  dayText: {
    fontSize: '14@ms',
    fontFamily: 'Urbanist-SemiBold',
    color: '#334155',
  },
  selectedDayText: {
    color: '#FFFFFF',
    fontFamily: 'Urbanist-Bold',
  },
  todayText: {
    color: Colors.primary,
    fontFamily: 'Urbanist-Bold',
  },
  disabledDayText: {
    color: '#CBD5E1',
  },
  otherMonthDayText: {
    color: '#94A3B8',
    opacity: 0.6,
  },
  smallScreenDayText: {
    fontSize: '12@ms',
  },
});

export default Calendar;
