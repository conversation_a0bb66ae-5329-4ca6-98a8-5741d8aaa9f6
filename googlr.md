import { useAuth } from '@/hooks/useAuth';
import { supabase } from '@/lib/supabase';
import { router, useLocalSearchParams } from 'expo-router';
import {
    ArrowLeft,
    Calendar,
    Clock,
    MapPin,
    Phone,
    User,
    Video,
} from 'lucide-react-native';
import React, { useEffect, useState } from 'react';
import {
    ActivityIndicator,
    Alert,
    Image,
    ScrollView,
    StyleSheet,
    Text,
    TouchableOpacity,
    View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { fonts } from '../_layout';

export default function AppointmentDetailsScreen() {
  const { id } = useLocalSearchParams();
  const { user } = useAuth();
  const [appointment, setAppointment] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (!id || !user) return;

    const fetchAppointment = async () => {
      setLoading(true);
      try {
        const { data, error } = await supabase
          .from('appointments')
          .select(
            `
            *,
            property:properties(*),
            agent:agents(*)
          `
          )
          .eq('id', id)
          .eq('user_id', user.id)
          .single();

        if (error) throw error;
        setAppointment(data);
      } catch (err: any) {
        console.error('Error fetching appointment details:', err);
        Alert.alert('Error', 'Failed to fetch appointment details.');
      } finally {
        setLoading(false);
      }
    };

    fetchAppointment();
  }, [id, user]);

  const getStatusStyle = (status: string) => {
    switch (status) {
      case 'confirmed':
        return {
          backgroundColor: '#D1FAE5',
          textColor: '#065F46',
          Icon: <View style={[styles.statusDot, { backgroundColor: '#10B981' }]} />,
        };
      case 'completed':
        return {
          backgroundColor: '#E0E7FF',
          textColor: '#4338CA',
          Icon: <View style={[styles.statusDot, { backgroundColor: '#6366F1' }]} />,
        };
      case 'cancelled':
        return {
          backgroundColor: '#FEE2E2',
          textColor: '#B91C1C',
          Icon: <View style={[styles.statusDot, { backgroundColor: '#EF4444' }]} />,
        };
      case 'pending':
      default:
        return {
          backgroundColor: '#FEF3C7',
          textColor: '#92400E',
          Icon: <View style={[styles.statusDot, { backgroundColor: '#F59E0B' }]} />,
        };
    }
  };
  
  const getContactIcon = (method: string) => {
    switch (method) {
      case 'in-person': return <MapPin size={20} color="#475569" />;
      case 'phone': return <Phone size={20} color="#475569" />;
      case 'video': return <Video size={20} color="#475569" />;
      default: return null;
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.centered}>
          <ActivityIndicator size="large" color="#4F46E5" />
        </View>
      </SafeAreaView>
    );
  }

  if (!appointment) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
            <ArrowLeft size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Appointment Not Found</Text>
          <View style={{width: 24}} />
        </View>
        <View style={styles.centered}>
          <Text>Could not find appointment details.</Text>
        </View>
      </SafeAreaView>
    );
  }

  const {
    status,
    appointment_date,
    contact_method,
    full_name,
    phone_number,
    notes,
    property,
  } = appointment;

  const statusStyle = getStatusStyle(status);

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity onPress={() => router.back()} style={styles.backButton}>
          <ArrowLeft size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Appointment Details</Text>
        <View style={{width: 24}}/>
      </View>

      <ScrollView contentContainerStyle={styles.scrollContainer}>
        <View style={styles.propertyHeader}>
            <Image
              source={{ uri: property?.images?.[0] || 'https://via.placeholder.com/400x200' }}
              style={styles.propertyImage}
            />
            <View style={styles.propertyImageOverlay} />
            <View style={styles.propertyInfo}>
              <Text style={styles.propertyTitle}>{property?.title}</Text>
              <Text style={styles.propertyLocation}>{property?.location}</Text>
            </View>
        </View>
        
        <View style={styles.card}>
            <Text style={styles.cardTitle}>Appointment Status</Text>
            <View style={[styles.statusBadge, { backgroundColor: statusStyle.backgroundColor }]}>
                {statusStyle.Icon}
                <Text style={[styles.statusText, { color: statusStyle.textColor }]}>{status}</Text>
            </View>
        </View>

        <View style={styles.card}>
            <Text style={styles.cardTitle}>Details</Text>
            <View style={styles.detailItem}>
                <Calendar size={20} color="#475569" />
                <Text style={styles.detailText}>{new Date(appointment_date).toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</Text>
            </View>
             <View style={styles.detailItem}>
                <Clock size={20} color="#475569" />
                <Text style={styles.detailText}>{new Date(appointment_date).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })}</Text>
            </View>
        </View>
        
        <View style={styles.card}>
            <Text style={styles.cardTitle}>Contact Information</Text>
            <View style={styles.detailItem}>
                {getContactIcon(contact_method)}
                <Text style={styles.detailText}>{contact_method.charAt(0).toUpperCase() + contact_method.slice(1)}</Text>
            </View>
            <View style={styles.detailItem}>
                <User size={20} color="#475569" />
                <Text style={styles.detailText}>{full_name}</Text>
            </View>
            <View style={styles.detailItem}>
                <Phone size={20} color="#475569" />
                <Text style={styles.detailText}>{phone_number}</Text>
            </View>
        </View>

        {notes && (
             <View style={styles.card}>
                <Text style={styles.cardTitle}>Additional Notes</Text>
                <Text style={styles.notesText}>{notes}</Text>
            </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F0F2F5',
  },
   centered: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 10,
    backgroundColor: '#1E293B',
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 20,
    fontFamily: fonts.heading.bold,
    color: '#FFFFFF',
  },
  scrollContainer: {
    paddingBottom: 24,
  },
  propertyHeader: {
    height: 180,
    position: 'relative',
    marginBottom: 16,
  },
  propertyImage: {
    width: '100%',
    height: '100%',
  },
  propertyImageOverlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0,0,0,0.4)',
  },
  propertyInfo: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 16,
  },
  propertyTitle: {
    fontSize: 22,
    fontFamily: fonts.heading.bold,
    color: '#FFFFFF',
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  propertyLocation: {
    fontSize: 16,
    fontFamily: fonts.body.regular,
    color: '#FFFFFF',
    marginTop: 4,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    padding: 16,
    marginHorizontal: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },
  cardTitle: {
    fontSize: 18,
    fontFamily: fonts.heading.semiBold,
    color: '#1E293B',
    marginBottom: 16,
  },
  statusBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 999,
    alignSelf: 'flex-start',
  },
  statusDot: {
      width: 8,
      height: 8,
      borderRadius: 4,
      marginRight: 8,
  },
  statusText: {
      fontSize: 14,
      fontFamily: fonts.body.semiBold,
      textTransform: 'capitalize',
  },
  detailItem: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: 12,
  },
  detailText: {
      fontSize: 16,
      fontFamily: fonts.body.regular,
      color: '#334155',
      marginLeft: 12,
  },
  notesText: {
      fontSize: 16,
      fontFamily: fonts.body.regular,
      color: '#475569',
      lineHeight: 24,
  }
}); 