{"name": "<PERSON><PERSON>", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "expo lint", "fix-fonts": "node ./scripts/fix-fonts.js"}, "jest": {"preset": "jest-expo", "transformIgnorePatterns": ["node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|@supabase/.*)"]}, "dependencies": {"@expo/config-plugins": "~10.1.1", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^5.1.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/cli-platform-android": "^18.0.0", "@react-native-community/netinfo": "11.4.1", "@react-native-picker/picker": "2.11.1", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/material-top-tabs": "^7.0.0", "@react-navigation/native": "^7.1.14", "@react-navigation/routers": "^7.0.0", "@react-navigation/stack": "^7.0.0", "@sentry/react-native": "~6.14.0", "@shopify/flash-list": "1.7.6", "@supabase/supabase-js": "^2.50.3", "base64-arraybuffer": "^1.0.2", "base64-js": "^1.5.1", "buffer": "^6.0.3", "expo": "53.0.17", "expo-auth-session": "~6.2.1", "expo-av": "~15.1.7", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.5", "expo-dev-client": "~5.2.4", "expo-device": "~7.1.4", "expo-document-picker": "~13.1.6", "expo-file-system": "~18.1.9", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image-manipulator": "~13.1.6", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-location": "~18.1.6", "expo-notifications": "~0.31.3", "expo-router": "~5.1.3", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "formik": "^2.4.6", "jest-expo": "~52.0.6", "js-sha512": "^0.9.0", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-confirmation-code-field": "^7.4.0", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "6.7.1", "react-native-paper": "^5.14.5", "react-native-paystack-webview": "^5.0.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-size-matters": "^0.4.2", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.0", "react-native-tab-view": "^4.0.5", "react-native-toast-message": "^2.2.1", "react-native-url-polyfill": "^2.0.0", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "resend": "^4.5.1", "yup": "^1.6.1", "zustand": "^5.0.6", "react-native-maps": "1.20.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@tsconfig/react-native": "^3.0.5", "@types/expo": "^32.0.13", "@types/jest": "^29.5.12", "@types/mime": "^3.0.4", "@types/react": "~19.0.0", "@types/react-test-renderer": "^19.0.0", "cross-env": "^7.0.3", "jest": "^29.2.1", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}